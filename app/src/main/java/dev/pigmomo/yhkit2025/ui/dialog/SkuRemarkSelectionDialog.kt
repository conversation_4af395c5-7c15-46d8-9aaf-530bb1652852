package dev.pigmomo.yhkit2025.ui.dialog

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import dev.pigmomo.yhkit2025.api.model.cart.AddToCartData
import dev.pigmomo.yhkit2025.api.model.cart.RemarkDetail

/**
 * SKU备注选择弹窗
 * 用于显示商品的备注选项，如宰杀方式等
 */
@Composable
fun SkuRemarkSelectionDialog(
    skuRemarkData: AddToCartData?,
    onDismiss: () -> Unit,
    onRemarkSelected: (String) -> Unit
) {
    if (skuRemarkData?.skuRemarkInfo == null) return

    var orderremark by remember {
        mutableStateOf(skuRemarkData.skuRemarkInfo.remarkDetailList[0].name)
    }

    Dialog(onDismissRequest = onDismiss) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(containerColor = Color.White)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(20.dp)
            ) {
                // 标题
                Text(
                    text = skuRemarkData.skuRemarkInfo.remarkName,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.Black,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(8.dp))

                // 商品信息
                Text(
                    text = skuRemarkData.title,
                    fontSize = 14.sp,
                    color = Color.Gray,
                    modifier = Modifier.fillMaxWidth(),
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.height(16.dp))

                // 备注选项列表
                LazyColumn(
                    modifier = Modifier.fillMaxWidth(),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(skuRemarkData.skuRemarkInfo.remarkDetailList) { remarkDetail ->
                        RemarkOptionItem(
                            remarkDetail = remarkDetail,
                            isSelected = orderremark == remarkDetail.name,
                            onSelected = { orderremark = remarkDetail.name }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(20.dp))

                // 按钮区域
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    // 取消按钮
                    OutlinedButton(
                        onClick = onDismiss,
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        colors = ButtonDefaults.outlinedButtonColors(
                            contentColor = Color.Gray
                        ),
                        border = BorderStroke(1.dp, Color.Gray)
                    ) {
                        Text(
                            text = "取消",
                            fontSize = 16.sp
                        )
                    }

                    // 确认按钮
                    Button(
                        onClick = { onRemarkSelected(orderremark) },
                        modifier = Modifier
                            .weight(1f)
                            .height(48.dp),
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFF4CAF50)
                        )
                    ) {
                        Text(
                            text = "确认",
                            fontSize = 16.sp,
                            color = Color.White
                        )
                    }
                }
            }
        }
    }
}

/**
 * 备注选项项目
 */
@Composable
private fun RemarkOptionItem(
    remarkDetail: RemarkDetail,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    val backgroundColor = if (isSelected) Color(0xFFE8F5E8) else Color.White
    val borderColor = if (isSelected) Color(0xFF4CAF50) else Color(0xFFE0E0E0)
    val textColor = if (isSelected) Color(0xFF4CAF50) else Color.Black

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(backgroundColor)
            .border(
                width = 1.dp,
                color = borderColor,
                shape = RoundedCornerShape(8.dp)
            )
            .clickable { onSelected() }
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 选项文本
            Text(
                text = remarkDetail.name,
                fontSize = 16.sp,
                color = textColor,
                modifier = Modifier.weight(1f)
            )

            // 选中指示器
            if (isSelected) {
                Box(
                    modifier = Modifier
                        .size(20.dp)
                        .clip(RoundedCornerShape(10.dp))
                        .background(Color(0xFF4CAF50)),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "✓",
                        color = Color.White,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }
    }
}
