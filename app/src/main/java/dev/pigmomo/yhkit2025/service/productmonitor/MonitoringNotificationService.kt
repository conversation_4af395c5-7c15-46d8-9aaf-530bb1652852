package dev.pigmomo.yhkit2025.service.productmonitor

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.core.app.NotificationCompat
import dev.pigmomo.yhkit2025.MainActivity
import dev.pigmomo.yhkit2025.R
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeRecordEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductChangeType
import dev.pigmomo.yhkit2025.data.model.productmonitor.ProductMonitorEntity
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch

/**
 * 监控通知服务
 * 用于发送监控变化通知
 *
 * 使用示例:
 * ```
 * // 配置通知行为
 * MonitoringNotificationService.configureNotifications(
 *     separateNotifications = true,  // 为每个变化分别发送通知
 *     showShopNameInTitle = true,    // 在标题中显示店铺名称
 *     priority = NotificationCompat.PRIORITY_HIGH,
 *     autoCancel = true
 * )
 *
 * // 发送商品变化通知
 * MonitoringNotificationService.sendProductChangeNotification(
 *     context = context,
 *     product = productEntity,
 *     changes = changesList
 * )
 * ```
 */
object MonitoringNotificationService {

    private const val TAG = "MonitoringNotificationService"
    private const val CHANNEL_ID = "monitoring_changes"
    private const val CHANNEL_NAME = "监控变化通知"
    private const val NOTIFICATION_ID_BASE = 2000

    private val notificationScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)

    /**
     * 通知配置
     */
    object NotificationConfig {
        // 是否为每个变化分别发送通知
        var separateNotifications: Boolean = true

        // 是否在通知标题中显示店铺名称
        var showShopNameInTitle: Boolean = true

        // 通知优先级
        var notificationPriority: Int = NotificationCompat.PRIORITY_DEFAULT

        // 是否自动取消通知
        var autoCancel: Boolean = true
    }

    // 通知事件流
    private val _notificationEvents = MutableSharedFlow<NotificationEvent>(
        replay = 0,
        extraBufferCapacity = 10
    )
    val notificationEvents: SharedFlow<NotificationEvent> = _notificationEvents.asSharedFlow()

    /**
     * 通知事件类型
     */
    sealed class NotificationEvent {
        data class ProductChanged(
            val product: ProductMonitorEntity,
            val changes: List<ProductChangeRecordEntity>
        ) : NotificationEvent()
    }

    /**
     * 初始化通知渠道
     */
    fun initializeNotificationChannel(context: Context) {
        val notificationManager =
            context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

        val channel = NotificationChannel(
            CHANNEL_ID,
            CHANNEL_NAME,
            NotificationManager.IMPORTANCE_DEFAULT
        ).apply {
            description = "商品监控变化通知"
            setShowBadge(true)
            enableVibration(true)
            enableLights(true)
        }

        notificationManager.createNotificationChannel(channel)
        Log.d(TAG, "Notification channel created: $CHANNEL_ID")
    }

    /**
     * 监听监控事件并发送通知
     */
    fun startListening(context: Context) {
        notificationScope.launch {
            MonitoringEventBus.events.collect { event ->
                Log.d(TAG, "收到监控事件: ${event::class.simpleName}")
                when (event) {
                    is MonitoringEventBus.MonitoringEvent.TaskExecuted -> {
                        Log.d(
                            TAG,
                            "处理任务执行完成事件: planId=${event.planId}, success=${event.result.success}, changes=${event.result.changesDetected}"
                        )
                        handleTaskExecuted(context, event.result)
                    }

                    else -> {
                        Log.d(TAG, "忽略其他事件: ${event::class.simpleName}")
                    }
                }
            }
        }
        Log.d(TAG, "Started listening for monitoring events")
    }

    /**
     * 处理单个任务执行完成事件
     */
    private suspend fun handleTaskExecuted(context: Context, result: MonitoringTaskResult) {
        Log.d(
            TAG,
            "处理任务执行结果: success=${result.success}, changesDetected=${result.changesDetected}, productChanges=${result.productChanges.size}"
        )

        if (result.success && result.changesDetected > 0) {
            Log.d(TAG, "Task executed with ${result.changesDetected} changes detected")

            // 只发送具体的商品变化通知
            if (result.productChanges.isNotEmpty()) {
                Log.d(TAG, "发送 ${result.productChanges.size} 个商品的变化通知")
                result.productChanges.forEach { (product, changes) ->
                    Log.d(TAG, "发送商品「${product.title}」的变化通知，变化数量: ${changes.size}")
                    // 根据配置决定通知方式
                    if (NotificationConfig.separateNotifications) {
                        sendSeparateChangeNotifications(context, product, changes)
                    } else {
                        sendDetailedProductChangeNotification(context, product, changes)
                    }
                }
            } else {
                Log.d(TAG, "没有具体的商品变化信息，跳过通知发送")
            }
        } else {
            Log.d(TAG, "任务执行失败或无变化，跳过通知发送")
        }
    }


    /**
     * 发送变化通知
     */
    private fun sendChangeNotification(
        context: Context,
        title: String,
        content: String,
        changeCount: Int
    ) {
        try {
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // 创建点击意图
            val intent = Intent(context, MainActivity::class.java).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
            }

            val pendingIntent = PendingIntent.getActivity(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 构建通知
            val notification = NotificationCompat.Builder(context, CHANNEL_ID)
                .setContentTitle(title)
                .setContentText(content)
                .setStyle(NotificationCompat.BigTextStyle().bigText(content))
                .setSmallIcon(R.drawable.ic_launcher_foreground)
                //.setContentIntent(pendingIntent)
                .setAutoCancel(NotificationConfig.autoCancel)
                .setPriority(NotificationConfig.notificationPriority)
                .setNumber(changeCount)
                .build()

            // 使用时间戳和随机数作为通知ID，确保每个通知都能显示
            val notificationId = NOTIFICATION_ID_BASE + (System.currentTimeMillis() % 10000).toInt() + (0..999).random()
            notificationManager.notify(notificationId, notification)

            Log.d(TAG, "Change notification sent: $title - $content")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to send change notification", e)
        }
    }

    /**
     * 为每个变化分别发送通知
     */
    private fun sendSeparateChangeNotifications(
        context: Context,
        product: ProductMonitorEntity,
        changes: List<ProductChangeRecordEntity>
    ) {
        val baseTitle = if (NotificationConfig.showShopNameInTitle && product.shopName.isNotEmpty()) {
            "${product.shopName} - ${product.title}"
        } else {
            product.title
        }

        // 为每个变化单独发送通知
        changes.forEach { change ->
            val changeContent = formatChangeInfoWithTime(change)
            sendChangeNotification(context, baseTitle, changeContent, 1)

            Log.d(TAG, "发送单个变化通知: $baseTitle - $changeContent")
        }

        // 发送商品变化事件（保持原有的事件机制）
        notificationScope.launch {
            _notificationEvents.tryEmit(
                NotificationEvent.ProductChanged(product, changes)
            )
        }
    }

    /**
     * 发送详细的商品变化通知（内部方法）
     * 保留此方法以支持合并通知的场景
     */
    private fun sendDetailedProductChangeNotification(
        context: Context,
        product: ProductMonitorEntity,
        changes: List<ProductChangeRecordEntity>
    ) {
        val title = if (NotificationConfig.showShopNameInTitle && product.shopName.isNotEmpty()) {
            "${product.shopName} - ${product.title}"
        } else {
            product.title
        }
        val content = buildString {
            changes.take(3).forEachIndexed { index, change ->
                append(formatChangeInfo(change))
                // 添加换行符（除了最后一个）
                if (index < minOf(2, changes.size - 1)) {
                    append("\n")
                }
            }
            // 如果变化超过3个，显示剩余数量
            if (changes.size > 3) {
                append("\n还有 ${changes.size - 3} 个变化")
            }
        }

        sendChangeNotification(context, title, content, changes.size)

        // 发送商品变化事件
        notificationScope.launch {
            _notificationEvents.tryEmit(
                NotificationEvent.ProductChanged(product, changes)
            )
        }
    }

    /**
     * 发送特定商品变化通知（公共接口）
     * @param context 上下文
     * @param product 商品信息
     * @param changes 变化记录列表
     * @param separateNotifications 是否为每个变化分别发送通知，默认使用配置值
     */
    fun sendProductChangeNotification(
        context: Context,
        product: ProductMonitorEntity,
        changes: List<ProductChangeRecordEntity>,
        separateNotifications: Boolean = NotificationConfig.separateNotifications
    ) {
        if (separateNotifications) {
            sendSeparateChangeNotifications(context, product, changes)
        } else {
            sendDetailedProductChangeNotification(context, product, changes)
        }
    }

    /**
     * 配置通知行为
     * @param separateNotifications 是否为每个变化分别发送通知
     * @param showShopNameInTitle 是否在标题中显示店铺名称
     * @param priority 通知优先级
     * @param autoCancel 是否自动取消通知
     */
    fun configureNotifications(
        separateNotifications: Boolean = true,
        showShopNameInTitle: Boolean = true,
        priority: Int = NotificationCompat.PRIORITY_DEFAULT,
        autoCancel: Boolean = true
    ) {
        NotificationConfig.separateNotifications = separateNotifications
        NotificationConfig.showShopNameInTitle = showShopNameInTitle
        NotificationConfig.notificationPriority = priority
        NotificationConfig.autoCancel = autoCancel

        Log.d(TAG, "通知配置已更新: separateNotifications=$separateNotifications, showShopNameInTitle=$showShopNameInTitle")
    }

    /**
     * 获取当前通知配置
     */
    fun getNotificationConfig(): Map<String, Any> {
        return mapOf(
            "separateNotifications" to NotificationConfig.separateNotifications,
            "showShopNameInTitle" to NotificationConfig.showShopNameInTitle,
            "notificationPriority" to NotificationConfig.notificationPriority,
            "autoCancel" to NotificationConfig.autoCancel
        )
    }

    /**
     * 格式化变化信息（带时间戳）
     */
    private fun formatChangeInfoWithTime(change: ProductChangeRecordEntity): String {
        val baseInfo = formatChangeInfo(change)
        val timeFormat = java.text.SimpleDateFormat("HH:mm:ss", java.util.Locale.getDefault())
        val timeStr = timeFormat.format(change.changeTime)
        return "$baseInfo (${timeStr})"
    }

    /**
     * 格式化变化信息
     */
    private fun formatChangeInfo(change: ProductChangeRecordEntity): String {
        return when (change.changeType) {
            ProductChangeType.PRICE_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        val oldPrice = change.oldValue.toDoubleOrNull()
                        val newPrice = change.newValue.toDoubleOrNull()
                        if (oldPrice != null && newPrice != null) {
                            val diff = newPrice - oldPrice
                            val symbol = if (diff > 0) "↗" else "↘"
                            "价格: ¥${change.oldValue} → ¥${change.newValue} $symbol"
                        } else {
                            "价格: ${change.oldValue} → ${change.newValue}"
                        }
                    }

                    change.newValue.isNotEmpty() -> "价格: ¥${change.newValue}"
                    else -> "价格变化"
                }
            }

            ProductChangeType.STOCK_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        "库存: ${change.oldValue} → ${change.newValue}"
                    }

                    change.newValue.isNotEmpty() -> "库存: ${change.newValue}"
                    else -> "库存变化"
                }
            }

            ProductChangeType.AVAILABILITY_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        val oldStatus = if (change.oldValue == "1") "可用" else "不可用"
                        val newStatus = if (change.newValue == "1") "可用" else "不可用"
                        "可用性: $oldStatus → $newStatus"
                    }

                    change.newValue.isNotEmpty() -> {
                        val status = if (change.newValue == "1") "可用" else "不可用"
                        "可用性: $status"
                    }

                    else -> "可用性变化"
                }
            }

            ProductChangeType.SECKILL_STATUS_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        val oldStatus = if (change.oldValue == "1") "是" else "否"
                        val newStatus = if (change.newValue == "1") "是" else "否"
                        "秒杀: $oldStatus → $newStatus"
                    }

                    change.newValue.isNotEmpty() -> {
                        val status = if (change.newValue == "1") "是" else "否"
                        "秒杀: $status"
                    }

                    else -> "秒杀状态变化"
                }
            }

            ProductChangeType.RESTRICT_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        "限购: ${change.oldValue} → ${change.newValue}"
                    }

                    change.newValue.isNotEmpty() -> "限购: ${change.newValue}"
                    else -> "限购变化"
                }
            }

            ProductChangeType.INFO_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        "${change.fieldName}: ${change.oldValue} → ${change.newValue}"
                    }

                    change.newValue.isNotEmpty() -> "${change.fieldName}: ${change.newValue}"
                    else -> "信息变化"
                }
            }

            ProductChangeType.OTHER_CHANGE -> {
                when {
                    change.oldValue.isNotEmpty() && change.newValue.isNotEmpty() -> {
                        "${change.fieldName}: ${change.oldValue} → ${change.newValue}"
                    }

                    change.newValue.isNotEmpty() -> "${change.fieldName}: ${change.newValue}"
                    else -> "其他变化"
                }
            }
        }
    }

    /**
     * 清除所有监控通知
     */
    fun clearAllNotifications(context: Context) {
        try {
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            // 清除监控相关的通知（ID范围 2000-2999）
            for (i in NOTIFICATION_ID_BASE until NOTIFICATION_ID_BASE + 1000) {
                notificationManager.cancel(i)
            }
            Log.d(TAG, "All monitoring notifications cleared")
        } catch (e: Exception) {
            Log.e(TAG, "Failed to clear notifications", e)
        }
    }
}
